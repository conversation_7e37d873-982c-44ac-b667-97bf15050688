"""
Core package - 核心功能模块

包含引擎的核心功能：
- world.py: 基础世界功能（相机、光照、地板等）
- selection.py: 选择和变换系统
- event_handler.py: 事件处理系统
- tool_manager.py: 工具管理系统
- script_system.py: 脚本系统
"""

from .world import CoreWorld
from .selection import SelectionSystem
from .event_handler import EventHandler
from .tool_manager import ToolManager
from .script_system import ScriptManager, ScriptBase, ScriptComponent

__all__ = [
    'CoreWorld', 
    'SelectionSystem', 
    'EventHandler', 
    'ToolManager',
    'ScriptManager',
    'ScriptBase',
    'ScriptComponent'
] 