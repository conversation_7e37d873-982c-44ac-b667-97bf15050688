#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
粗糙度和金属性贴图修复测试脚本
"""

def test_roughness_texture_logic():
    """测试粗糙度贴图逻辑"""
    print("=== 粗糙度贴图逻辑测试 ===")
    
    print("RenderPipeline粗糙度计算公式:")
    print("  最终粗糙度 = 材质粗糙度 × 贴图值")
    print("")
    
    # 模拟不同的材质粗糙度值
    test_cases = [
        {"material_roughness": 0.0, "texture_value": 0.5, "expected_effect": "无效果（材质粗糙度为0）"},
        {"material_roughness": 0.3, "texture_value": 0.5, "expected_effect": "0.15（较光滑）"},
        {"material_roughness": 1.0, "texture_value": 0.5, "expected_effect": "0.5（中等粗糙）"},
        {"material_roughness": 1.0, "texture_value": 1.0, "expected_effect": "1.0（最粗糙）"},
    ]
    
    print("粗糙度贴图效果测试用例:")
    for i, case in enumerate(test_cases, 1):
        material_r = case["material_roughness"]
        texture_v = case["texture_value"]
        expected = case["expected_effect"]
        final_roughness = material_r * texture_v
        print(f"  {i}. 材质:{material_r} × 贴图:{texture_v} = {final_roughness} ({expected})")
    
    print("\n修复策略:")
    print("  - 如果材质粗糙度 ≤ 0.01，自动设置为 1.0")
    print("  - 这样贴图值可以完全控制最终粗糙度")
    
    return True

def test_metallic_texture_logic():
    """测试金属性贴图逻辑"""
    print("\n=== 金属性贴图逻辑测试 ===")
    
    print("标准RenderPipeline问题:")
    print("  - gbuffer.frag.glsl中只使用 m.metallic = mInput.metallic")
    print("  - 没有从贴图采样金属性值")
    print("")
    
    print("修复方案:")
    print("  1. 创建自定义shader效果 (pbr_with_metallic.yaml)")
    print("  2. 使用p3d_Texture5作为金属性贴图槽")
    print("  3. 计算公式: 最终金属性 = 材质金属性 × 贴图值")
    print("")
    
    # 模拟不同的材质金属性值
    test_cases = [
        {"material_metallic": 0.0, "texture_value": 1.0, "expected_effect": "无效果（材质金属性为0）"},
        {"material_metallic": 0.5, "texture_value": 1.0, "expected_effect": "0.5（半金属）"},
        {"material_metallic": 1.0, "texture_value": 0.5, "expected_effect": "0.5（半金属）"},
        {"material_metallic": 1.0, "texture_value": 1.0, "expected_effect": "1.0（全金属）"},
    ]
    
    print("金属性贴图效果测试用例:")
    for i, case in enumerate(test_cases, 1):
        material_m = case["material_metallic"]
        texture_v = case["texture_value"]
        expected = case["expected_effect"]
        final_metallic = material_m * texture_v
        print(f"  {i}. 材质:{material_m} × 贴图:{texture_v} = {final_metallic} ({expected})")
    
    print("\n修复策略:")
    print("  - 如果材质金属性 ≤ 0.01，自动设置为 1.0")
    print("  - 使用自定义shader支持金属性贴图")
    
    return True

def test_texture_slot_mapping():
    """测试纹理槽映射"""
    print("\n=== 纹理槽映射测试 ===")
    
    texture_slots = {
        "p3d_Texture0": "漫反射贴图 (Sort=0)",
        "p3d_Texture1": "法线贴图 (Sort=1)",
        "p3d_Texture2": "IOR贴图 (Sort=2)",
        "p3d_Texture3": "粗糙度贴图 (Sort=3)",
        "p3d_Texture4": "视差贴图 (Sort=4)",
        "p3d_Texture5": "金属性贴图 (Sort=5, 自定义)"
    }
    
    print("完整的纹理槽映射:")
    for slot, description in texture_slots.items():
        print(f"  {slot}: {description}")
    
    return True

def test_material_property_requirements():
    """测试材质属性要求"""
    print("\n=== 材质属性要求测试 ===")
    
    requirements = {
        "粗糙度贴图": {
            "material_property": "roughness",
            "minimum_value": 0.01,
            "recommended_value": 1.0,
            "reason": "shader中使用乘法，材质值为0时贴图无效"
        },
        "金属性贴图": {
            "material_property": "metallic",
            "minimum_value": 0.01,
            "recommended_value": 1.0,
            "reason": "自定义shader中使用乘法，材质值为0时贴图无效"
        },
        "法线贴图": {
            "material_property": "normalfactor",
            "minimum_value": 0.1,
            "recommended_value": 1.0,
            "reason": "控制法线强度，通过emission.y设置"
        }
    }
    
    print("贴图类型的材质属性要求:")
    for texture_type, req in requirements.items():
        print(f"\n  {texture_type}:")
        print(f"    材质属性: {req['material_property']}")
        print(f"    最小值: {req['minimum_value']}")
        print(f"    推荐值: {req['recommended_value']}")
        print(f"    原因: {req['reason']}")
    
    return True

def print_troubleshooting_guide():
    """打印故障排除指南"""
    print("\n" + "="*60)
    print("粗糙度和金属性贴图故障排除指南")
    print("="*60)
    
    print("""
常见问题及解决方案:

1. 粗糙度贴图没有效果:
   ✓ 检查材质的roughness值是否 > 0
   ✓ 确保贴图是灰度图（白色=粗糙，黑色=光滑）
   ✓ 检查贴图是否正确加载到p3d_Texture3槽

2. 金属性贴图没有效果:
   ✓ 检查材质的metallic值是否 > 0
   ✓ 确保使用了自定义shader效果
   ✓ 检查贴图是否正确加载到p3d_Texture5槽

3. 贴图效果太弱:
   ✓ 将对应的材质属性设置为1.0
   ✓ 检查贴图的对比度是否足够

4. 贴图效果异常:
   ✓ 确保贴图格式正确（通常为灰度图）
   ✓ 检查UV坐标是否正确
   ✓ 确保光照设置合适

调试步骤:
1. 查看控制台输出的纹理阶段信息
2. 检查材质属性值是否被正确设置
3. 验证贴图文件是否正确加载
4. 测试不同的材质属性值组合
""")

def main():
    """主测试函数"""
    print("粗糙度和金属性贴图修复测试开始...")
    
    tests = [
        test_roughness_texture_logic,
        test_metallic_texture_logic,
        test_texture_slot_mapping,
        test_material_property_requirements
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ 测试通过")
            else:
                print("✗ 测试失败")
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    # 打印故障排除指南
    print_troubleshooting_guide()
    
    print("\n" + "="*60)
    print("测试完成！")
    print("="*60)

if __name__ == "__main__":
    main()
