<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>680</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>1000</width>
    <height>680</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>1000</width>
    <height>680</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Plugin Configurator</string>
  </property>
  <property name="windowIcon">
   <iconset resource="resources.qrc">
    <normaloff>:/icon/res/icon.png</normaloff>:/icon/res/icon.png</iconset>
  </property>
  <property name="styleSheet">
   <string notr="true">QMainWindow { background: #fff;}
*, QLabel { font-family: Roboto; font-weight: 300; }

QScrollBar {
	background: #eee;
}


QScrollBar:vertical {
                        width: 9px;
	margin: 0;
                      }

                      QScrollBar::handle:vertical {
                        min-height: 15px;
						background: #aaa;

                      }


                      QScrollBar::handle:vertical:hover {
                        
						background: #999;

                      }
                      QScrollBar::add-line:vertical {
                      }

                      QScrollBar::sub-line:vertical {
                      }


                      QScrollBar::add-page:vertical {
                        background: #ddd;

                      }
                      QScrollBar::sub-page:vertical {
                        background: #ddd;
					}
</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QListWidget" name="lst_plugins">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>80</y>
      <width>251</width>
      <height>531</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">QListWidget::item {
padding: 8px 7px 5px;
background: #ddd;
outline: 0 !important;
margin-bottom: 1px;
margin-right: 10px;
color: #777;
font-weight: 500;
border: 0;
border-radius: 0;
}

QListWidget::item:hover {
background: #ccc;
}

QListWidget::item:selected {
background: #555;
color: #eee;
}

QListWidget {
padding: 0;
color: #eee;
background: transparent;
/*
background: #222;
padding: 5px 8px;
border: 1px solid #444;
*/
}

* {
outline: 0;
}

</string>
    </property>
    <property name="frameShape">
     <enum>QFrame::NoFrame</enum>
    </property>
    <property name="lineWidth">
     <number>0</number>
    </property>
    <item>
     <property name="text">
      <string>Ambient Occlusion</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Another Ite</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Bloom</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Volumetric Clouds</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Depth of Field</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Environment Probes</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>FXAA (Antialiasing)</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>SKAA</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Motion Blur</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>PSSM Shadows</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Atmospheric Scattering</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Skin Shading</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>SMAA (Antialiasing)</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Screen Space Reflections</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Volumetric Lighting</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>VXGI</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Item 346</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Other Item</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Other Item to force scrollbars</string>
     </property>
    </item>
   </widget>
   <widget class="QFrame" name="frame_details">
    <property name="geometry">
     <rect>
      <x>281</x>
      <y>60</y>
      <width>721</width>
      <height>621</height>
     </rect>
    </property>
    <property name="palette">
     <palette>
      <active>
       <colorrole role="WindowText">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>255</red>
          <green>255</green>
          <blue>255</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Button">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>229</red>
          <green>229</green>
          <blue>229</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Light">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>63</red>
          <green>63</green>
          <blue>63</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Midlight">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>52</red>
          <green>52</green>
          <blue>52</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Dark">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>21</red>
          <green>21</green>
          <blue>21</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Mid">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>28</red>
          <green>28</green>
          <blue>28</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Text">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>255</red>
          <green>255</green>
          <blue>255</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="BrightText">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>255</red>
          <green>255</green>
          <blue>255</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="ButtonText">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>255</red>
          <green>255</green>
          <blue>255</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Base">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>229</red>
          <green>229</green>
          <blue>229</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Window">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>229</red>
          <green>229</green>
          <blue>229</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Shadow">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>0</red>
          <green>0</green>
          <blue>0</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="AlternateBase">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>21</red>
          <green>21</green>
          <blue>21</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="ToolTipBase">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>255</red>
          <green>255</green>
          <blue>220</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="ToolTipText">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>0</red>
          <green>0</green>
          <blue>0</blue>
         </color>
        </brush>
       </colorrole>
      </active>
      <inactive>
       <colorrole role="WindowText">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>255</red>
          <green>255</green>
          <blue>255</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Button">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>229</red>
          <green>229</green>
          <blue>229</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Light">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>63</red>
          <green>63</green>
          <blue>63</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Midlight">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>52</red>
          <green>52</green>
          <blue>52</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Dark">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>21</red>
          <green>21</green>
          <blue>21</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Mid">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>28</red>
          <green>28</green>
          <blue>28</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Text">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>255</red>
          <green>255</green>
          <blue>255</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="BrightText">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>255</red>
          <green>255</green>
          <blue>255</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="ButtonText">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>255</red>
          <green>255</green>
          <blue>255</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Base">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>229</red>
          <green>229</green>
          <blue>229</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Window">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>229</red>
          <green>229</green>
          <blue>229</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Shadow">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>0</red>
          <green>0</green>
          <blue>0</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="AlternateBase">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>21</red>
          <green>21</green>
          <blue>21</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="ToolTipBase">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>255</red>
          <green>255</green>
          <blue>220</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="ToolTipText">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>0</red>
          <green>0</green>
          <blue>0</blue>
         </color>
        </brush>
       </colorrole>
      </inactive>
      <disabled>
       <colorrole role="WindowText">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>21</red>
          <green>21</green>
          <blue>21</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Button">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>229</red>
          <green>229</green>
          <blue>229</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Light">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>63</red>
          <green>63</green>
          <blue>63</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Midlight">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>52</red>
          <green>52</green>
          <blue>52</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Dark">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>21</red>
          <green>21</green>
          <blue>21</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Mid">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>28</red>
          <green>28</green>
          <blue>28</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Text">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>21</red>
          <green>21</green>
          <blue>21</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="BrightText">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>255</red>
          <green>255</green>
          <blue>255</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="ButtonText">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>21</red>
          <green>21</green>
          <blue>21</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Base">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>229</red>
          <green>229</green>
          <blue>229</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Window">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>229</red>
          <green>229</green>
          <blue>229</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="Shadow">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>0</red>
          <green>0</green>
          <blue>0</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="AlternateBase">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>42</red>
          <green>42</green>
          <blue>42</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="ToolTipBase">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>255</red>
          <green>255</green>
          <blue>220</blue>
         </color>
        </brush>
       </colorrole>
       <colorrole role="ToolTipText">
        <brush brushstyle="SolidPattern">
         <color alpha="255">
          <red>0</red>
          <green>0</green>
          <blue>0</blue>
         </color>
        </brush>
       </colorrole>
      </disabled>
     </palette>
    </property>
    <property name="autoFillBackground">
     <bool>false</bool>
    </property>
    <property name="styleSheet">
     <string notr="true">#frame_details { background: #e5e5e5; }</string>
    </property>
    <property name="frameShape">
     <enum>QFrame::NoFrame</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Plain</enum>
    </property>
    <property name="lineWidth">
     <number>1</number>
    </property>
    <widget class="QLabel" name="lbl_plugin_version">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>40</y>
       <width>391</width>
       <height>21</height>
      </rect>
     </property>
     <property name="palette">
      <palette>
       <active>
        <colorrole role="WindowText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>151</green>
           <blue>25</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Button">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>236</red>
           <green>236</green>
           <blue>236</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Text">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>151</green>
           <blue>25</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ButtonText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>151</green>
           <blue>25</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Base">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>236</red>
           <green>236</green>
           <blue>236</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Window">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>236</red>
           <green>236</green>
           <blue>236</blue>
          </color>
         </brush>
        </colorrole>
       </active>
       <inactive>
        <colorrole role="WindowText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>151</green>
           <blue>25</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Button">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>236</red>
           <green>236</green>
           <blue>236</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Text">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>151</green>
           <blue>25</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ButtonText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>151</green>
           <blue>25</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Base">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>236</red>
           <green>236</green>
           <blue>236</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Window">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>236</red>
           <green>236</green>
           <blue>236</blue>
          </color>
         </brush>
        </colorrole>
       </inactive>
       <disabled>
        <colorrole role="WindowText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>151</green>
           <blue>25</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Button">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>236</red>
           <green>236</green>
           <blue>236</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Text">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>151</green>
           <blue>25</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ButtonText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>151</green>
           <blue>25</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Base">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>236</red>
           <green>236</green>
           <blue>236</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Window">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>236</red>
           <green>236</green>
           <blue>236</blue>
          </color>
         </brush>
        </colorrole>
       </disabled>
      </palette>
     </property>
     <property name="font">
      <font>
       <family>Roboto</family>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">color: #ff9719; border: 0; border-radius: 4px; font-weight: bold;</string>
     </property>
     <property name="text">
      <string>version 0.1 by some author</string>
     </property>
     <property name="textFormat">
      <enum>Qt::PlainText</enum>
     </property>
     <property name="alignment">
      <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
     </property>
    </widget>
    <widget class="QLabel" name="lbl_plugin_desc">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>60</y>
       <width>441</width>
       <height>51</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">color: #777; border: 0; font-size: 11px;</string>
     </property>
     <property name="text">
      <string>This is a fancy description
It shows information about the plugin and maybe
a website or so.</string>
     </property>
     <property name="textFormat">
      <enum>Qt::PlainText</enum>
     </property>
     <property name="alignment">
      <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
     </property>
     <property name="wordWrap">
      <bool>true</bool>
     </property>
    </widget>
    <widget class="QTableWidget" name="table_plugin_settings">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>110</y>
       <width>681</width>
       <height>491</height>
      </rect>
     </property>
     <property name="palette">
      <palette>
       <active>
        <colorrole role="WindowText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Button">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>240</red>
           <green>240</green>
           <blue>240</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Light">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Midlight">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>247</red>
           <green>247</green>
           <blue>247</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Dark">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>120</red>
           <green>120</green>
           <blue>120</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Mid">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>160</red>
           <green>160</green>
           <blue>160</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Text">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="BrightText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ButtonText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Base">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Window">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>240</red>
           <green>240</green>
           <blue>240</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Shadow">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="AlternateBase">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>247</red>
           <green>247</green>
           <blue>247</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ToolTipBase">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>220</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ToolTipText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
       </active>
       <inactive>
        <colorrole role="WindowText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Button">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>240</red>
           <green>240</green>
           <blue>240</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Light">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Midlight">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>247</red>
           <green>247</green>
           <blue>247</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Dark">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>120</red>
           <green>120</green>
           <blue>120</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Mid">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>160</red>
           <green>160</green>
           <blue>160</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Text">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="BrightText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ButtonText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Base">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Window">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>240</red>
           <green>240</green>
           <blue>240</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Shadow">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="AlternateBase">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>247</red>
           <green>247</green>
           <blue>247</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ToolTipBase">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>220</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ToolTipText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
       </inactive>
       <disabled>
        <colorrole role="WindowText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>120</red>
           <green>120</green>
           <blue>120</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Button">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>240</red>
           <green>240</green>
           <blue>240</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Light">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Midlight">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>247</red>
           <green>247</green>
           <blue>247</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Dark">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>120</red>
           <green>120</green>
           <blue>120</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Mid">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>160</red>
           <green>160</green>
           <blue>160</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Text">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>120</red>
           <green>120</green>
           <blue>120</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="BrightText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>255</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ButtonText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>120</red>
           <green>120</green>
           <blue>120</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Base">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>240</red>
           <green>240</green>
           <blue>240</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Window">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>240</red>
           <green>240</green>
           <blue>240</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Shadow">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="AlternateBase">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>240</red>
           <green>240</green>
           <blue>240</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ToolTipBase">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>255</red>
           <green>255</green>
           <blue>220</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ToolTipText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>0</red>
           <green>0</green>
           <blue>0</blue>
          </color>
         </brush>
        </colorrole>
       </disabled>
      </palette>
     </property>
     <property name="font">
      <font>
       <family>Roboto</family>
       <pointsize>-1</pointsize>
       <weight>37</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QTableWidget { border: 1px solid #ccc; font-family: Roboto; font-size: 11px; }</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="verticalScrollBarPolicy">
      <enum>Qt::ScrollBarAsNeeded</enum>
     </property>
     <property name="horizontalScrollBarPolicy">
      <enum>Qt::ScrollBarAlwaysOff</enum>
     </property>
     <property name="editTriggers">
      <set>QAbstractItemView::NoEditTriggers</set>
     </property>
     <property name="showDropIndicator" stdset="0">
      <bool>false</bool>
     </property>
     <property name="dragDropOverwriteMode">
      <bool>false</bool>
     </property>
     <property name="alternatingRowColors">
      <bool>true</bool>
     </property>
     <property name="selectionMode">
      <enum>QAbstractItemView::NoSelection</enum>
     </property>
     <property name="selectionBehavior">
      <enum>QAbstractItemView::SelectRows</enum>
     </property>
     <property name="textElideMode">
      <enum>Qt::ElideNone</enum>
     </property>
     <property name="verticalScrollMode">
      <enum>QAbstractItemView::ScrollPerPixel</enum>
     </property>
     <property name="horizontalScrollMode">
      <enum>QAbstractItemView::ScrollPerPixel</enum>
     </property>
     <property name="showGrid">
      <bool>false</bool>
     </property>
     <property name="gridStyle">
      <enum>Qt::NoPen</enum>
     </property>
     <property name="wordWrap">
      <bool>true</bool>
     </property>
     <property name="cornerButtonEnabled">
      <bool>true</bool>
     </property>
     <attribute name="horizontalHeaderVisible">
      <bool>true</bool>
     </attribute>
     <attribute name="horizontalHeaderCascadingSectionResizes">
      <bool>false</bool>
     </attribute>
     <attribute name="horizontalHeaderHighlightSections">
      <bool>true</bool>
     </attribute>
     <attribute name="horizontalHeaderShowSortIndicator" stdset="0">
      <bool>false</bool>
     </attribute>
     <attribute name="horizontalHeaderStretchLastSection">
      <bool>true</bool>
     </attribute>
     <attribute name="verticalHeaderVisible">
      <bool>false</bool>
     </attribute>
     <attribute name="verticalHeaderCascadingSectionResizes">
      <bool>false</bool>
     </attribute>
     <attribute name="verticalHeaderDefaultSectionSize">
      <number>45</number>
     </attribute>
     <attribute name="verticalHeaderShowSortIndicator" stdset="0">
      <bool>false</bool>
     </attribute>
     <attribute name="verticalHeaderStretchLastSection">
      <bool>false</bool>
     </attribute>
     <row>
      <property name="text">
       <string>Row</string>
      </property>
      <property name="textAlignment">
       <set>AlignHCenter|AlignVCenter|AlignCenter</set>
      </property>
     </row>
     <row>
      <property name="text">
       <string>Row2</string>
      </property>
     </row>
     <column>
      <property name="text">
       <string>Setting</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Default</string>
      </property>
      <property name="textAlignment">
       <set>AlignHCenter|AlignVCenter|AlignCenter</set>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Current Value</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Description</string>
      </property>
     </column>
     <item row="0" column="0">
      <property name="text">
       <string>Long setting name which requires</string>
      </property>
     </item>
     <item row="0" column="1">
      <property name="text">
       <string>DefaultRow0</string>
      </property>
     </item>
     <item row="0" column="2">
      <property name="text">
       <string>CurrentRow0</string>
      </property>
     </item>
     <item row="0" column="3">
      <property name="text">
       <string>DescRow0</string>
      </property>
     </item>
     <item row="1" column="1">
      <property name="text">
       <string>DefaultRow1</string>
      </property>
     </item>
     <item row="1" column="2">
      <property name="text">
       <string>CurrentRow1</string>
      </property>
     </item>
     <item row="1" column="3">
      <property name="text">
       <string>aasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfgaasd\n\nsdf\n\ndfdfG\n\nfg</string>
      </property>
     </item>
    </widget>
    <widget class="QLabel" name="lbl_plugin_name">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>20</y>
       <width>391</width>
       <height>21</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>Roboto</family>
       <pointsize>-1</pointsize>
       <weight>37</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">color: #555; border: 0;  font-size: 15px;</string>
     </property>
     <property name="text">
      <string>Ambient Occlusion</string>
     </property>
     <property name="textFormat">
      <enum>Qt::RichText</enum>
     </property>
    </widget>
    <widget class="QPushButton" name="btn_reset_plugin_settings">
     <property name="geometry">
      <rect>
       <x>520</x>
       <y>20</y>
       <width>180</width>
       <height>31</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
color: #eee;
background: #666;
border: 0; 
}

QPushButton:hover {
	background: #555;
}

QPushButton:pressed {
	background: #111;
}</string>
     </property>
     <property name="text">
      <string>Reset Settings of this Plugin</string>
     </property>
    </widget>
   </widget>
   <widget class="QFrame" name="frame">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>60</y>
      <width>281</width>
      <height>621</height>
     </rect>
    </property>
    <property name="autoFillBackground">
     <bool>false</bool>
    </property>
    <property name="styleSheet">
     <string notr="true">QFrame {background: #eee;}</string>
    </property>
    <property name="frameShape">
     <enum>QFrame::StyledPanel</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Raised</enum>
    </property>
    <widget class="QLabel" name="lbl_hint_restart">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>560</y>
       <width>251</width>
       <height>51</height>
      </rect>
     </property>
     <property name="palette">
      <palette>
       <active>
        <colorrole role="WindowText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>153</red>
           <green>153</green>
           <blue>153</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Button">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>238</red>
           <green>238</green>
           <blue>238</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Text">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>153</red>
           <green>153</green>
           <blue>153</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ButtonText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>153</red>
           <green>153</green>
           <blue>153</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Base">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>238</red>
           <green>238</green>
           <blue>238</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Window">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>238</red>
           <green>238</green>
           <blue>238</blue>
          </color>
         </brush>
        </colorrole>
       </active>
       <inactive>
        <colorrole role="WindowText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>153</red>
           <green>153</green>
           <blue>153</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Button">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>238</red>
           <green>238</green>
           <blue>238</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Text">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>153</red>
           <green>153</green>
           <blue>153</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ButtonText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>153</red>
           <green>153</green>
           <blue>153</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Base">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>238</red>
           <green>238</green>
           <blue>238</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Window">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>238</red>
           <green>238</green>
           <blue>238</blue>
          </color>
         </brush>
        </colorrole>
       </inactive>
       <disabled>
        <colorrole role="WindowText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>153</red>
           <green>153</green>
           <blue>153</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Button">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>238</red>
           <green>238</green>
           <blue>238</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Text">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>153</red>
           <green>153</green>
           <blue>153</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="ButtonText">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>153</red>
           <green>153</green>
           <blue>153</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Base">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>238</red>
           <green>238</green>
           <blue>238</blue>
          </color>
         </brush>
        </colorrole>
        <colorrole role="Window">
         <brush brushstyle="SolidPattern">
          <color alpha="255">
           <red>238</red>
           <green>238</green>
           <blue>238</blue>
          </color>
         </brush>
        </colorrole>
       </disabled>
      </palette>
     </property>
     <property name="font">
      <font>
       <family>Roboto</family>
       <weight>37</weight>
       <bold>false</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">border-top: 1px dotted #aaa; color: #999; padding: 0;</string>
     </property>
     <property name="text">
      <string>Hint: Settings with a gray color require a pipeline restart when changed.</string>
     </property>
     <property name="textFormat">
      <enum>Qt::PlainText</enum>
     </property>
     <property name="scaledContents">
      <bool>false</bool>
     </property>
     <property name="alignment">
      <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
     </property>
     <property name="wordWrap">
      <bool>true</bool>
     </property>
    </widget>
   </widget>
   <widget class="QLabel" name="lbl_restart_pipeline">
    <property name="geometry">
     <rect>
      <x>687</x>
      <y>18</y>
      <width>294</width>
      <height>28</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>Roboto</family>
      <pointsize>-1</pointsize>
      <weight>37</weight>
      <bold>false</bold>
     </font>
    </property>
    <property name="autoFillBackground">
     <bool>false</bool>
    </property>
    <property name="styleSheet">
     <string notr="true">background: #ff9719; color: #eee; padding: 5px; border: 0; font-size: 12px;</string>
    </property>
    <property name="text">
     <string>Pipeline needs to be restarted to apply all changes!</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
   <widget class="QLabel" name="label">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>25</y>
      <width>261</width>
      <height>31</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">font-size: 20px; color: #ff9719;
font-weight: 100;</string>
    </property>
    <property name="text">
     <string>PLUGIN CONFIGURATOR</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_9">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>6</y>
      <width>261</width>
      <height>31</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">font-size: 13px;
color: #aaa;
font-weight: 600;</string>
    </property>
    <property name="text">
     <string>RENDER PIPELINE</string>
    </property>
   </widget>
   <zorder>frame</zorder>
   <zorder>lst_plugins</zorder>
   <zorder>lbl_restart_pipeline</zorder>
   <zorder>frame_details</zorder>
   <zorder>label</zorder>
   <zorder>label_9</zorder>
  </widget>
 </widget>
 <resources>
  <include location="resources.qrc"/>
 </resources>
 <connections/>
</ui>
