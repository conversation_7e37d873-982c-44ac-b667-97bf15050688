# PBR effect with metallic texture support
# This effect extends the default PBR pipeline to support metallic textures

fragment:
    defines: |
        #define USE_METALLIC_TEXTURE 1

    inout: |
        uniform sampler2D p3d_Texture5;  // Metallic texture

    material: |
        // Fetch metallic value from texture if available
        #if USE_METALLIC_TEXTURE
            float sampled_metallic = texture(p3d_Texture5, texcoord).x;
            m.metallic = mInput.metallic * sampled_metallic;
        #else
            m.metallic = mInput.metallic;
        #endif
